"""

------更新记录----  
------更新记录----  
要  微信社群的抽奖
请60秒内 可能会黑 只能社群的抽奖  找/搜
https://d2capplet.app.com.cn/activity/webapi/v2/redirect/jump2BrandCny 

例如https://d2capplet.ap....openId=...third_ts=1852...57#

openId#nickname#avatar#tuid#备注
openId # nickname # avatar # tuid #备注

感觉好用   可以打赏下  发我支付宝口令 
@hwjiejie


这个脚本是 原来是收费版 脚本   好用   可以打赏下  发我支付宝口令 

"""
import requests
import json
#通知
from fake_useragent import UserAgent
import time
import random
import os
from datetime import datetime




fstz = 1  # 1 表示发送通知，0 表示不发送通知

sjsz = (1, 2)  # 随机休眠时间范围（秒）

log_messages = []  # 用于存储日志信息

excluded_prizes = {"5悦币", "3月会员 满99-25", "3月企微满79-10元", "3月企微满39-5元", "3月会员满79-10元", "3月企微满99-15", "3月会员满99-15", "3月会员满39-5元"}  # 排除的奖品




#-------------------------------请求-------------
#本地测试用 
acid = "0d58eb03-7165-46ab-aaf2-2664bbf258a2#e92a115c-5014-45b5-81c4-5a207d8a4558"
aid2_jjb = "" #抽奖2   精简版抽奖#  这个不需要头像
#aid2_jjb = "17ce8a6a-a3e3-46f2-a786-c33c941cd42b"  #抽奖2   精简版抽奖#  这个不需要头像
#

#本地测试用 
os.environ['qfzycjck1'] = '''

'''




UA ="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c11)XWEB/11275"
def sc_ua():#生成UA
    user_agent = UserAgent()
    return user_agent.random

def create_headers():
    headers = {
        "User-Agent": sc_ua(),
        "sec-fetch-dest": "document",
        "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "priority": "u=0, i"
    }
    return headers

# 创建请求头的函数
def create_headers1(token, referer):
    headers = {
        "Host": "d2capplet.app.com.cn",
        "User-Agent": sc_ua(),
        "sec-ch-ua": '"Not/A)Brand";v="8", "Chromium";v="126", "Android WebView";v="126"',
        "usertoken": token,
        "sec-ch-ua-mobile": "?1",
        "referer": referer,
        "cookie": f"userToken={token}",  # 仅设置 userToken  # 合并两个 cookie
        "priority": "u=1, i"
    }
    return headers

# 通用请求函数
def urly(url, headers, method="GET", data=None):
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method.upper() == "POST_DA":
            response = requests.post(url, headers=headers, data=data)
        else:
            raise ValueError("仅支持 GET, POST 和 POST_DA 请求")
        
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP 错误：{http_err}")
    except Exception as err:
        print(f"请求异常：{err}")
        return None

#-------------------------------上面请求-------------
def tj_sj_common(op_id, ac_id, ni_me=None, av_ar=None, tuid=None, mode="default"):
    base_url = "https://d2capplet.app.com.cn/activity/webapi/v2/redirect/jump2BrandCny"
    current_timestamp = str(int(time.time() * 1000))
    
    # 过滤空的 ac_id
    if not ac_id:
        print("活动ID为空，跳过抽奖")
        return []

    ac_ids = ac_id.split("#")
    p_values = []
    
    # 添加随机延迟，防止触发反爬机制
    delay = random.randint(1, 3)  # 延迟时间范围可以调节
    time.sleep(delay)

    for aid in ac_ids:
        # print(f"当前使用的 activityId: {aid}")
        params = {
            "openId": op_id,
            "activityId": aid,
            "brand": "jgapp",
            "third_ts": current_timestamp
        }

        # 根据模式设置额外参数
        if mode == "default":
            # 如果昵称或头像为None，不加入
            if ni_me: params.update({"nickname": ni_me})
            if av_ar: params.update({"avatar": av_ar})
            if tuid: params.update({"tuid": tuid})
        elif mode == "simple":
            # 精简模式，不需要昵称、头像等参数
            pass

        try:
            response = requests.get(base_url, headers=create_headers(), params=params, allow_redirects=False)
            if response.status_code == 302:
                location = response.headers.get("location")
                if location:
                    p_value = location.split("?p=")[-1]
                    p_values.append(p_value)
                  #  print(f"成功获取 p_value: {p_value}")  # 打印出 p_value 进行调试
                else:
                    print("未找到 location 头信息")
            else:
                print(f"非 302 响应状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"请求异常：{e}")
    
    if not p_values:
        print("没有成功获取任何 p_value")
        
    return p_values

def tj_sj_common1(op_id, ac_id, ni_me=None, av_ar=None, tuid=None, mode="default"):

    base_url = "https://d2capplet.app.com.cn/activity/webapi/v2/redirect/jump2BrandCny"
    current_timestamp = str(int(time.time() * 1000))
    ac_ids = ac_id.split("#")
    p_values = []
    
    # 添加随机延迟，防止触发反爬机制
    delay = random.randint(1, 2)
    time.sleep(delay)

    for aid in ac_ids:
       # print(f"当前使用的 activityId: {aid}")
        params = {
            "openId": op_id,
            "activityId": aid,
            "brand": "jgapp",
            "third_ts": current_timestamp
        }

        # 根据模式设置额外参数
        if mode == "default":
            params.update({"nickname": ni_me, "avatar": av_ar, "tuid": tuid})
        elif mode == "simple":
            # 精简模式，不需要昵称、头像等参数
            pass

        try:
            response = requests.get(base_url, headers=create_headers(), params=params, allow_redirects=False)
            if response.status_code == 302:
                location = response.headers.get("location")
                if location:
                    p_value = location.split("?p=")[-1]
                    p_values.append(p_value)
                   # print(p_value) #需要在打印
                else:
                    print("未找到 location 头信息")
            else:
                print(f"非 302 响应状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"请求异常：{e}")
    return p_values

def login_with_p(p_value):

    login_url = f"https://d2capplet.app.com.cn/activity/webapi/v2/user/login?p={p_value}"
    headers = create_headers()
    response = urly(login_url, headers, method="GET")
    
    if response and response.get("code") == 0:
        data = response.get("data", {})
        nickname = data.get("nickname")
        token = data.get("token")
       # print(f"nickname: {nickname}, token: {token}")
        return nickname, token
    else:
        print("登录失败或响应数据格式错误。")
        return None, None

# 抽奖请求函数
def cx_mf_cjjh(token, referer, account_info, nickname):

    url = "https://d2capplet.app.com.cn/activity/webapi/v2/activity/checkDrawPrize"
    headers = create_headers1(token, referer)  # 创建请求头
    response = urly(url, headers, method="GET")
    if response and response.get("code") == 0:
        data = response.get("data", {})
        freeNum = data.get("freeNum", 0)  # 免费抽奖次数
        consumeType = data.get("consumeType", "未知")  # 消费类型

        if freeNum > 0:
            # 如果有免费次数，执行抽奖操作
            draw_prize(token, referer, account_info, nickname)
        else:
            print(f"{account_info}, {nickname}无免费")
            # 记录无免费抽奖次数的信息
          
            #log_message = f"{account_info}, {nickname} 无免费抽奖次数"
            #log_messages.append(log_message)
    else:
        # 抽奖请求失败，记录信息
        print("登录失败或响应数据格式错误。")


def draw_prize(token, referer, account_info, nickname, max_retries=6):

    url = "https://d2capplet.app.com.cn/activity/webapi/v2/activity/drawPrize"
    headers = create_headers1(token, referer)
    retries = 0

    while retries < max_retries:
        # 随机延迟
        delay = random.randint(sjsz[0], sjsz[1])
        time.sleep(delay)
        
        # 发起请求，空白请求体
        response = urly(url, headers, method="POST", data={})
        
        if response:
            code = response.get("code", None)  # 获取响应中的 code 值
            
            if code == 0:
                # 成功抽奖
                name = response["data"].get("name", "未知奖品")
               # print(f"{nickname} 抽奖成功！奖品名称：{name}")
                if name not in excluded_prizes:  # 检查奖品是否需要记录
                    log_message = f"{nickname} 抽奖成功！奖品名称：{name}"
                    log_messages.append(log_message)
                    print(log_message)
                else:
                    #print(f"{nickname} 抽中排除奖品：{name}，不记录日志。")
                    print(f"{nickname} 奖品：{name}。")
                return response
            elif code == ********:
                # 网络错误或其他临时性问题，重试
                msg = response.get("msg", "未知错误")
               # print(f"错误********：{nickname} {msg}，第 {retries + 1} 次重试中...")
                print(f"第 {retries + 1} 次重试中... ")
               # print(f"{nickname}错误代码******** ， 第 {retries + 1} 次重试中... ")
                retries += 1
            elif code == ********:
                # 抽奖上限
                msg = response.get("msg", "未知错误")
                print(f"错误004：{nickname} {account_info} {msg}")
                break
            else:
                # 未知错误代码
                print(f"未知响应代码：{code}，响应内容：{response}")
                break
        else:
            # 无响应或请求失败
            print(f"抽奖请求失败或无响应内容，第 {retries + 1} 次重试中...")
            retries += 1
    else:
        print(f"已达到最大重试次数 {max_retries} 次，抽奖结束。")

#  上面是新代码




# 主函数
def main():#1.2版
    """
    主函数，处理多个账户的抽奖流程
    """
    tokens = bj_ll_ms(bl_ql_sz)
    if not tokens:
        print(f"环境变量 {bl_ql_sz} 未设置，请检查。")
        return

    total_accounts = len(tokens)
    for i, token in enumerate(tokens):
        parts = token.split('#')
        if len(parts) < 1:
            print("令牌格式不正确，跳过处理。")
            continue

        op_id = parts[0]
        ni_me = parts[1]
        av_ar = parts[2]
        tuid = parts[3]
        account_info = parts[4] if len(parts) > 4 else "没有"

        print(f"正在处理账号 {i+1}/{total_accounts}，昵称: {account_info}")

        # 定义抽奖模式和对应的活动 ID
        modes = [
            {"mode": "default", "ac_id": acid},
            {"mode": "simple", "ac_id": aid2_jjb}
        ]

        for config in modes:
            # 通用抽奖请求
            p_values = tj_sj_common(op_id, config["ac_id"], ni_me, av_ar, tuid, mode=config["mode"])
            for p_value in p_values:
                if p_value:
                    nickname, token = login_with_p(p_value)
                    if token:
                        referer_url = f"https://d2capplet.app.com.cn/gashapon/?p={p_value}"
                        cx_mf_cjjh(token, referer_url, account_info, nickname)

    # 所有账号运行完成后统一发送通知
    jie_qltz()

def main1():#1.1版
    """
    主函数，处理多个账户的抽奖流程
    """
    tokens = bj_ll_ms(bl_ql_sz)
    if not tokens:
        print(f"环境变量 {bl_ql_sz} 未设置，请检查。")
        return

    total_accounts = len(tokens)
    for i, token in enumerate(tokens):
        parts = token.split('#')
        if len(parts) < 1:
            print("令牌格式不正确，跳过处理。")
            continue

        op_id = parts[0]
        ni_me = parts[1]
        av_ar = parts[2]
        tuid = parts[3]
        account_info = parts[4] if len(parts) > 4 else "没有"

        # 第一个抽奖请求
        p_values_1 = tj_sj_common(op_id, acid, ni_me, av_ar, tuid, mode="default")
        for p_value in p_values_1:
            if p_value:
                nickname, token = login_with_p(p_value)
                if token:
                    referer_url = f"https://d2capplet.app.com.cn/gashapon/?p={p_value}"
                    cx_mf_cjjh(token, referer_url, account_info, nickname)

        # 第二个抽奖请求（精简模式）
        p_values_2 = tj_sj_common(op_id, aid2_jjb, mode="simple")
        for p_value in p_values_2:
            if p_value:
                nickname, token = login_with_p(p_value)
                if token:
                    referer_url = f"https://d2capplet.app.com.cn/gashapon/?p={p_value}"
                    cx_mf_cjjh(token, referer_url, account_info, nickname)

    # 所有账号运行完成后统一发送通知
    jie_qltz()







#---------简化的框架 0.72 带通知--------
bl_ql_sz = 'qfzhck'      # 环境变量名称，储存账号信息
jbxmmz = "纸悦-抽奖"
jbxmbb = "1.2"
jbbbsj = "2025年1月22日21:03:37"





def log(message):
    print(message)

def print_disclaimer():
    log("📢 请认真阅读以下声明")
    log("      【免责声明】         ")
    log("✨ 脚本及其中涉及的任何解密分析程序，仅用于测试和学习研究")
    log("✨ 禁止用于商业用途，不能保证其合法性，准确性，完整性和有效性，请根据情况自行判断")
    log("✨ 禁止任何公众号、自媒体进行任何形式的转载、发布")
    log("✨ 本人对任何脚本问题概不负责，包括但不限于由任何脚本错误导致的任何损失或损害")
    log("✨ 脚本文件请在下载试用后24小时内自行删除")
    log("✨ 脚本文件如有不慎被破解或修改由破解或修改者承担")
    log("✨ 如不接受此条款请立即删除脚本文件")
    log("" * 10)
    log("如果喜欢请打赏支持维护和开发  ")
    log("" * 10)
    log(f'这个是怎么东西？？？')
    
    log("" * 10)
    log("" * 10)
    log(f'-----------{jbxmmz} {jbxmbb}版本  {jbbbsj} -----------')


# 获取环境变量
def bj_ll_ms(var_name):#读取账号信息 1.1版   

    def parse_accounts(data, delimiter="&"):
        """将字符串或列表数据拆分为独立账号并去除空白"""
        split_data = data.replace(delimiter, '\n').split('\n') if isinstance(data, str) else data
        return [item.strip() for item in split_data if item.strip()]

    def load_json(file_name):
        """从 JSON 文件加载账号数据"""
        try:
            with open(file_name, "r", encoding="utf-8") as f:
                accounts = json.load(f)
                result = []
                for key, value in accounts.items():
                    cks = [value[field] for field in value.keys() if field.lower().startswith("ck") and value[field]]
                    result.append(f"{'#'.join(cks)}#{key}")  # 拼接所有 ck 字段和 key
                return result
        except json.JSONDecodeError as e:
            print(f"JSON 文件解析失败: {e}")
            return []

    def load_txt(file_name):
        """从 TXT 文件加载账号数据"""
        with open(file_name, "r", encoding="utf-8") as f:
            return f.read().strip().split('\n')

    processed_accounts = []
    json_file_name = f"{var_name}.json"  # JSON 文件名
    txt_file_name = f"{var_name}.txt"   # TXT 文件名

    if os.path.exists(json_file_name):  # 检查 JSON 文件
        print(f"检测到 JSON 文件 {json_file_name}，从文件中读取账号信息...")
        processed_accounts = load_json(json_file_name)
    elif os.path.exists(txt_file_name):  # 检查 TXT 文件
        print(f"检测到 TXT 文件 {txt_file_name}，从文件中读取账号信息...")
        processed_accounts = parse_accounts("\n".join(load_txt(txt_file_name)))
    else:  # 从环境变量中读取
        print(f"文件 {json_file_name} 和 {txt_file_name} 不存在，从环境变量 {var_name} 中读取...")
        env_value = os.getenv(var_name)
        if env_value is None:
            print(f"环境变量 {var_name} 未设置，请检查。")
            return []
        processed_accounts = parse_accounts(env_value)

    print(f"解析后账号数量：{len(processed_accounts)}")
    return processed_accounts




def jie_qltz():
    """
    使用全局变量 jbxmmz 执行测试脚本。
    """
   
   # print(log_messages) #需要在打开
    combined_message = "\n\n".join(log_messages)  # 将所有日志信息组合成一个字符串
    #print(combined_message)  # 调试视图
    
    # 根据 fstz 的值决定是否发送通知
    if fstz == 1:
        QLAPI.notify(jbxmmz, combined_message)
        print("通知已发送。")
    else:
        print("设置不需要 发送通知 。")


#------------------
if __name__ == "__main__":
    main()