/**
 * 清风纸悦抽奖脚本 - 
 * 作者：Tianxx
 * 版本：2.0
 * 日期：2025-08-01
 */

const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
const APPID = 'wx6b9ceb6542d232be'; // 清风纸悦小程序appid
const RANDOM_DELAY = [1, 2]; // 随机延迟范围（秒）

// 排除的奖品关键词（按原版逻辑）
const EXCLUSION_KEYWORDS = ["会员满", "企微满", "会员 满", "元优惠券", "优惠券", "悦币"];

// 活动ID配置
const ACTIVITY_CONFIG = {
    acid: "0d58eb03-7165-46ab-aaf2-2664bbf258a2#e92a115c-5014-45b5-81c4-5a207d8a4558",
    aid2_jjb: "" // 精简版抽奖ID
};

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 引入依赖模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// 获取脚本名称
const scriptName = path.basename(__filename, '.js');
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];
    return wxidString
        .split('\n')
        .map(wxid => wxid.trim())
        .filter(wxid => wxid.length > 0)
        .filter(wxid => !wxid.startsWith('#'));
}

// HTTP请求函数
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const protocol = options.protocol === 'https:' ? https : http;
        const req = protocol.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const result = JSON.parse(body);
                    resolve(result);
                } catch (e) {
                    resolve(body);
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

// 生成随机User-Agent
function generateUserAgent() {
    return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185";
}

// 随机延迟函数
function randomDelay() {
    const delay = Math.floor(Math.random() * (RANDOM_DELAY[1] - RANDOM_DELAY[0] + 1)) + RANDOM_DELAY[0];
    return new Promise(resolve => setTimeout(resolve, delay * 1000));
}

class QingFengZhiYue {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.isLogin = false;
        this.wxCode = null;
        this.openid = null;
        this.unionid = null;
        this.token = null;
        this.memberId = null;
        this.cacheExpireTime = null;
        this.logMessages = [];
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    this.wxCode = userCache.wxCode;
                    this.openid = userCache.openid;
                    this.unionid = userCache.unionid;
                    this.token = userCache.token;
                    this.memberId = userCache.memberId;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载数据成功`);
                        console.log(`[DEBUG] OpenID: ${this.openid}`);
                        console.log(`[DEBUG] Token: ${this.token}`);
                    }
                    return true;
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存token缓存
    saveTokenCache() {
        try {
            let cacheData = {};
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
            }

            // 使用实际的过期时间，如果没有则设置1小时
            const expireTime = this.cacheExpireTime || (Date.now() + 60 * 60 * 1000);

            cacheData[this.wxid] = {
                wxCode: this.wxCode,
                openid: this.openid,
                unionid: this.unionid,
                token: this.token,
                memberId: this.memberId,
                cacheExpireTime: expireTime
            };

            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 新的登录方法 - 基于提供的接口
    async loginWithNewAPI() {
        try {
            if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

            // 1. 获取微信code
            const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
            if (!codeResult.success) {
                console.log(`获取授权码失败：${codeResult.error}`);
                return false;
            }

            this.wxCode = codeResult.code;
            if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);

            // 2. 使用新的登录接口
            const loginOptions = {
                hostname: 'd2capplet.app.com.cn',
                port: 443,
                path: '/miniprogram-api/sinar-api/auth/member/login',
                method: 'POST',
                protocol: 'https:',
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    'User-Agent': generateUserAgent(),
                    'xweb_xhr': '1',
                    'authorization': 'Bearer',
                    'accept': '*/*',
                    'sec-fetch-site': 'cross-site',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-dest': 'empty',
                    'referer': 'https://servicewechat.com/wx6b9ceb6542d232be/287/page-frame.html',
                    'accept-encoding': 'gzip, deflate, br',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'priority': 'u=1, i'
                }
            };

            const loginData = { code: this.wxCode };
            const loginResponse = await makeRequest(loginOptions, loginData);

            if (loginResponse.code === 200 && loginResponse.data) {
                const data = loginResponse.data;
                this.openid = data.openid;
                this.unionid = data.unionid;
                this.token = data.token;
                this.memberId = data.memberId;
                // 使用接口返回的过期时间，如果没有则默认1小时
                this.cacheExpireTime = data.expireTime || (Date.now() + 60 * 60 * 1000);
                this.isLogin = true;

                if (isDebug) {
                    console.log(`[DEBUG] 登录成功`);
                    console.log(`[DEBUG] OpenID: ${this.openid}`);
                    console.log(`[DEBUG] UnionID: ${this.unionid}`);
                    console.log(`[DEBUG] MemberID: ${this.memberId}`);
                    console.log(`[DEBUG] Token过期时间: ${new Date(this.cacheExpireTime).toLocaleString()}`);
                }

                // 保存到缓存
                this.saveTokenCache();
                return true;
            } else {
                console.log(`登录失败: ${loginResponse.msg || '未知错误'}`);
                return false;
            }
        } catch (error) {
            console.log(`登录异常: ${error.message}`);
            return false;
        }
    }

    // 生成抽奖跳转链接并获取p_value
    async generateLotteryParams(activityId, mode = "default") {
        if (!activityId) {
            console.log("活动ID为空，跳过抽奖");
            return [];
        }

        const baseUrl = "https://d2capplet.app.com.cn/activity/webapi/v2/redirect/jump2BrandCny";
        const currentTimestamp = Date.now().toString();
        const activityIds = activityId.split("#");
        const pValues = [];

        // 添加随机延迟
        await randomDelay();

        for (const aid of activityIds) {
            const params = new URLSearchParams({
                openId: this.openid,
                activityId: aid,
                brand: "jgapp",
                third_ts: currentTimestamp
            });

            // 根据模式设置额外参数
            if (mode === "default") {
                // 这里可以添加昵称、头像等参数，但新接口可能不需要
            }

            try {
                const options = {
                    hostname: 'd2capplet.app.com.cn',
                    port: 443,
                    path: `/activity/webapi/v2/redirect/jump2BrandCny?${params.toString()}`,
                    method: 'GET',
                    protocol: 'https:',
                    headers: {
                        'User-Agent': generateUserAgent(),
                        'sec-fetch-dest': 'document',
                        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                        'priority': 'u=0, i'
                    }
                };

                const response = await new Promise((resolve, reject) => {
                    const protocol = options.protocol === 'https:' ? https : http;
                    const req = protocol.request(options, (res) => {
                        resolve(res);
                    });
                    req.on('error', reject);
                    req.end();
                });

                if (response.statusCode === 302) {
                    const location = response.headers.location;
                    if (location) {
                        const pValue = location.split("?p=")[1];
                        if (pValue) {
                            pValues.push(pValue);
                            if (isDebug) console.log(`[DEBUG] 成功获取 p_value: ${pValue}`);
                        }
                    }
                } else {
                    console.log(`非 302 响应状态码: ${response.statusCode}`);
                }
            } catch (error) {
                console.log(`请求异常：${error.message}`);
            }
        }

        return pValues;
    }

    // 使用p_value登录获取抽奖token
    async loginWithPValue(pValue) {
        try {
            const options = {
                hostname: 'd2capplet.app.com.cn',
                port: 443,
                path: `/activity/webapi/v2/user/login?p=${pValue}`,
                method: 'GET',
                protocol: 'https:',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.179 Mobile Safari/537.36 XWEB/1380083 MMWEBSDK/******** MMWEBID/2783 MicroMessenger/8.0.61.2880(0x28003D57) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 miniProgram/wx6b9ceb6542d232be',
                    'accept': 'application/json',
                    'referer': `https://d2capplet.app.com.cn/gashapon/?p=${pValue}`
                }
            };

            const response = await makeRequest(options);

            if (response && response.code === 0) {
                const data = response.data || {};
                return {
                    nickname: data.nickname,
                    token: data.token,
                    pValue: pValue // 返回pValue用于后续请求
                };
            } else {
                if (isDebug) console.log(`[DEBUG] 抽奖登录失败: ${response?.msg || '未知错误'}`);
                return { nickname: null, token: null, pValue: null };
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 抽奖登录异常: ${error.message}`);
            return { nickname: null, token: null, pValue: null };
        }
    }

    // 检查抽奖次数
    async checkDrawPrize(token, referer, accountInfo, nickname) {
        try {
            const options = {
                hostname: 'd2capplet.app.com.cn',
                port: 443,
                path: '/activity/webapi/v2/activity/checkDrawPrize',
                method: 'GET',
                protocol: 'https:',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.179 Mobile Safari/537.36 XWEB/1380083 MMWEBSDK/******** MMWEBID/2783 MicroMessenger/8.0.61.2880(0x28003D57) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 miniProgram/wx6b9ceb6542d232be',
                    'usertoken': token,
                    'referer': referer
                }
            };

            const response = await makeRequest(options);

            if (response && response.code === 0) {
                const data = response.data || {};
                const freeNum = data.freeNum || 0; // 使用 freeNum 模式

                if (freeNum > 0) {
                    console.log(`${nickname} 有 ${freeNum} 次免费抽奖机会`);
                    // 只抽奖免费次数
                    for (let i = 0; i < freeNum; i++) {
                        await randomDelay(); // 随机延迟
                        await this.drawPrize(token, referer, accountInfo, nickname);
                    }
                    return true; // 有免费次数并已抽奖
                } else {
                    console.log(`${accountInfo}, ${nickname} 无免费抽奖次数`);
                    return false; // 无免费次数
                }
            } else {
                console.log(`检查抽奖次数失败: ${response?.msg || '未知错误'}`);
                return false;
            }
        } catch (error) {
            console.log(`检查抽奖次数异常: ${error.message}`);
            return false;
        }
    }

    // 执行抽奖
    async drawPrize(token, referer, accountInfo, nickname, maxRetries = 6) {
        const url = '/activity/webapi/v2/activity/drawPrize';
        let retries = 0;

        while (retries < maxRetries) {
            try {
                // 随机延迟
                await randomDelay();

                const options = {
                    hostname: 'd2capplet.app.com.cn',
                    port: 443,
                    path: url,
                    method: 'POST',
                    protocol: 'https:',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.179 Mobile Safari/537.36 XWEB/1380083 MMWEBSDK/******** MMWEBID/2783 MicroMessenger/8.0.61.2880(0x28003D57) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 miniProgram/wx6b9ceb6542d232be',
                        'usertoken': token,
                        'referer': referer,
                        'Content-Type': 'application/json'
                    }
                };

                const response = await makeRequest(options, {});

                if (response) {
                    const code = response.code;

                    if (code === 0) {
                        // 抽奖成功
                        const name = response.data?.name || "未知奖品";
                        console.log(`${nickname} 抽奖：${name}`);

                        // 按原版逻辑检查是否需要通知
                        const shouldNotify = !EXCLUSION_KEYWORDS.some(keyword => name.includes(keyword));

                        if (shouldNotify) {
                            const logMessage = `${nickname} 抽奖成功！奖品名称：${name}`;
                            this.logMessages.push(logMessage);
                        }
                        return response;
                    } else if (code === ********) {
                        // 网络错误，重试
                        console.log(`第 ${retries + 1} 次重试中...`);
                        retries++;
                    } else if (code === ********) {
                        // 抽奖上限
                        const msg = response.msg || "未知错误";
                        console.log(`错误004：${nickname} ${accountInfo} ${msg}`);
                        break;
                    } else {
                        // 未知错误
                        console.log(`未知响应代码：${code}，响应内容：${JSON.stringify(response)}`);
                        break;
                    }
                } else {
                    console.log(`抽奖请求失败或无响应内容，第 ${retries + 1} 次重试中...`);
                    retries++;
                }
            } catch (error) {
                console.log(`抽奖请求异常：${error.message}，第 ${retries + 1} 次重试中...`);
                retries++;
            }
        }

        if (retries >= maxRetries) {
            console.log(`已达到最大重试次数 ${maxRetries} 次，抽奖结束。`);
        }
    }

    // 执行完整的抽奖流程
    async performLottery() {
        if (!this.isLogin) {
            console.log("未登录，无法执行抽奖");
            return false;
        }

        console.log(`开始执行抽奖流程...`);

        // 定义抽奖模式和对应的活动ID
        const modes = [
            { mode: "default", activityId: ACTIVITY_CONFIG.acid },
            { mode: "simple", activityId: ACTIVITY_CONFIG.aid2_jjb }
        ];

        for (const config of modes) {
            if (!config.activityId) {
                if (isDebug) console.log(`[DEBUG] ${config.mode} 模式活动ID为空，跳过`);
                continue;
            }

            console.log(`执行 ${config.mode} 模式抽奖...`);

            // 生成抽奖参数
            const pValues = await this.generateLotteryParams(config.activityId, config.mode);

            let hasDrawn = false; // 标记是否已经抽过奖

            for (const pValue of pValues) {
                if (pValue) {
                    // 使用p_value登录获取抽奖token
                    const { nickname, token } = await this.loginWithPValue(pValue);

                    if (token) {
                        const refererUrl = `https://d2capplet.app.com.cn/gashapon/?p=${pValue}`;
                        const drawResult = await this.checkDrawPrize(token, refererUrl, this.wxid, nickname || this.wxid);

                        if (drawResult) {
                            hasDrawn = true;
                            break; // 抽奖成功后跳出循环，不再处理其他p_value
                        } else if (nickname) {
                            // 如果有昵称说明登录成功但无免费次数，也跳出循环
                            break;
                        }
                    }
                }
            }

            // 如果这个模式已经抽过奖，跳过后续模式
            if (hasDrawn) {
                if (isDebug) console.log(`[DEBUG] ${config.mode} 模式已抽奖，跳过后续模式`);
                break;
            }
        }

        return true;
    }

    // 验证缓存数据是否有效
    async validateCache() {
        if (!this.token) return false;

        try {
            // 这里可以添加验证token有效性的逻辑
            // 暂时返回true，假设缓存数据有效
            return true;
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 缓存验证失败: ${error.message}`);
            return false;
        }
    }

    // 主要业务逻辑
    async run() {
        try {
            console.log(`🚀 开始处理账号: ${this.wxid}`);

            // 1. 尝试从缓存加载数据
            const cacheLoaded = this.loadTokenCache();

            if (cacheLoaded) {
                console.log(`📦 使用缓存的数据`);

                // 验证缓存数据是否仍然有效
                const cacheValid = await this.validateCache();
                if (!cacheValid) {
                    console.log(`⚠️ 缓存的数据已失效，重新登录...`);
                    const loginSuccess = await this.loginWithNewAPI();
                    if (!loginSuccess) {
                        console.log(`[${this.wxid}] 登录失败，跳过`);
                        return;
                    }
                } else {
                    console.log(`✅ 缓存的数据有效`);
                }
            } else {
                // 2. 缓存无效或不存在，进行登录
                const loginSuccess = await this.loginWithNewAPI();
                if (!loginSuccess) {
                    console.log(`[${this.wxid}] 登录失败，跳过`);
                    return;
                }
            }

            // 3. 执行抽奖流程
            await this.performLottery();

            console.log(`✅ 账号 ${this.wxid} 处理完成`);
        } catch (error) {
            console.log(`[${this.wxid}] 脚本执行出错：${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }
    }
}

// 通知相关变量和函数
let notice = '';

function print(msg, isNotice = false) {
    const str = `${msg}`;
    console.log(str);
    if (NOTICE_SWITCH && isNotice) {
        notice += `${str}\n`;
    }
}

async function sendMsg(message) {
    try {
        let notify = '';
        try {
            notify = require('./sendNotify');
        } catch (e) {
            try {
                notify = require("../sendNotify");
            } catch (e2) {
                console.log('❌ 未找到sendNotify模块，无法发送通知');
                return;
            }
        }
        await notify.sendNotify(scriptName, message);
        console.log('📢 通知发送成功');
    } catch (error) {
        console.log(`❌ 通知发送失败: ${error.message}`);
    }
}

// 主函数
async function main() {
    console.log(`🔔 清风纸悦抽奖脚本开始执行`);
    console.log(`📋 脚本版本: 2.0 整合版`);
    console.log(`📅 执行时间: ${new Date().toLocaleString()}`);

    if (isDebug) {
        console.log(`[DEBUG] 调试模式已开启`);
        console.log(`[DEBUG] APPID: ${APPID}`);
    }

    if (!wxidList) {
        console.log(`❌ 未设置环境变量 TXX_WXID 或命令行参数 --wxid`);
        return;
    }

    // 处理单个wxid或多个wxid
    const wxids = cmdWxid ? [cmdWxid] : parseWxidList(wxidList);

    if (wxids.length === 0) {
        console.log(`❌ 没有找到有效的wxid`);
        return;
    }

    console.log(`📋 共找到 ${wxids.length} 个有效账号`);

    if (isDebug) {
        console.log(`[DEBUG] 账号列表: ${wxids.join(', ')}`);
    }

    const allLogMessages = [];

    // 逐个处理账号
    for (let i = 0; i < wxids.length; i++) {
        const wxid = wxids[i];
        console.log(`\n🚀 [${i + 1}/${wxids.length}] 开始处理账号: ${wxid}`);

        try {
            const script = new QingFengZhiYue(wxid);
            await script.run();

            // 收集日志消息
            if (script.logMessages.length > 0) {
                allLogMessages.push(...script.logMessages);
            }

            console.log(`✅ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理完成`);
        } catch (error) {
            console.log(`❌ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }

        console.log('─'.repeat(60));

        // 如果不是最后一个账号，稍微延迟一下
        if (i < wxids.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    console.log(`\n🎉 所有账号处理完成！`);

    // 发送通知 - 只有真正中奖时才发送
    if (NOTICE_SWITCH && allLogMessages.length > 0) {
        const combinedMessage = allLogMessages.join('\n\n');
        await sendMsg(combinedMessage);
        console.log('📢 发现中奖记录，已发送通知');
    } else {
        console.log('📝 本次无有价值中奖记录，不发送通知');
    }
}

// 执行脚本
main().catch(console.error);
