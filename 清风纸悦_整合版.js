/**
 * 清风纸悦抽奖脚本 - 整合版
 * 整合了Python脚本的抽奖功能和新的登录接口
 * 作者：整合版本
 * 版本：2.0
 * 日期：2025-08-01
 */

const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
const APPID = 'wx6b9ceb6542d232be'; // 清风纸悦小程序appid
const RANDOM_DELAY = [1, 2]; // 随机延迟范围（秒）

// 排除的奖品列表
const EXCLUDED_PRIZES = new Set([
    "5悦币", "3月会员 满99-25", "3月企微满79-10元", 
    "3月企微满39-5元", "3月会员满79-10元", "3月企微满99-15", 
    "3月会员满99-15", "3月会员满39-5元"
]);

// 活动ID配置
const ACTIVITY_CONFIG = {
    acid: "0d58eb03-7165-46ab-aaf2-2664bbf258a2#e92a115c-5014-45b5-81c4-5a207d8a4558",
    aid2_jjb: "" // 精简版抽奖ID
};

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 引入依赖模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// 获取脚本名称
const scriptName = path.basename(__filename, '.js');
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];
    return wxidString
        .split('\n')
        .map(wxid => wxid.trim())
        .filter(wxid => wxid.length > 0)
        .filter(wxid => !wxid.startsWith('#'));
}

// HTTP请求函数
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const protocol = options.protocol === 'https:' ? https : http;
        const req = protocol.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const result = JSON.parse(body);
                    resolve(result);
                } catch (e) {
                    resolve(body);
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

// 生成随机User-Agent
function generateUserAgent() {
    return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185";
}

// 随机延迟函数
function randomDelay() {
    const delay = Math.floor(Math.random() * (RANDOM_DELAY[1] - RANDOM_DELAY[0] + 1)) + RANDOM_DELAY[0];
    return new Promise(resolve => setTimeout(resolve, delay * 1000));
}

class QingFengZhiYue {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.isLogin = false;
        this.wxCode = null;
        this.openid = null;
        this.unionid = null;
        this.token = null;
        this.memberId = null;
        this.cacheExpireTime = null;
        this.logMessages = [];
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    this.wxCode = userCache.wxCode;
                    this.openid = userCache.openid;
                    this.unionid = userCache.unionid;
                    this.token = userCache.token;
                    this.memberId = userCache.memberId;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载数据成功`);
                        console.log(`[DEBUG] OpenID: ${this.openid}`);
                        console.log(`[DEBUG] Token: ${this.token}`);
                    }
                    return true;
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存token缓存
    saveTokenCache() {
        try {
            let cacheData = {};
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
            }

            // 设置过期时间（2小时）
            const expireTime = Date.now() + (2 * 60 * 60 * 1000);

            cacheData[this.wxid] = {
                wxCode: this.wxCode,
                openid: this.openid,
                unionid: this.unionid,
                token: this.token,
                memberId: this.memberId,
                cacheExpireTime: expireTime
            };

            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 新的登录方法 - 基于提供的接口
    async loginWithNewAPI() {
        try {
            if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

            // 1. 获取微信code
            const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
            if (!codeResult.success) {
                console.log(`获取授权码失败：${codeResult.error}`);
                return false;
            }

            this.wxCode = codeResult.code;
            if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);

            // 2. 使用新的登录接口
            const loginOptions = {
                hostname: 'd2capplet.app.com.cn',
                port: 443,
                path: '/miniprogram-api/sinar-api/auth/member/login',
                method: 'POST',
                protocol: 'https:',
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    'User-Agent': generateUserAgent(),
                    'xweb_xhr': '1',
                    'authorization': 'Bearer',
                    'accept': '*/*',
                    'sec-fetch-site': 'cross-site',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-dest': 'empty',
                    'referer': 'https://servicewechat.com/wx6b9ceb6542d232be/287/page-frame.html',
                    'accept-encoding': 'gzip, deflate, br',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'priority': 'u=1, i'
                }
            };

            const loginData = { code: this.wxCode };
            const loginResponse = await makeRequest(loginOptions, loginData);

            if (loginResponse.code === 200 && loginResponse.data) {
                const data = loginResponse.data;
                this.openid = data.openid;
                this.unionid = data.unionid;
                this.token = data.token;
                this.memberId = data.memberId;
                this.isLogin = true;

                if (isDebug) {
                    console.log(`[DEBUG] 登录成功`);
                    console.log(`[DEBUG] OpenID: ${this.openid}`);
                    console.log(`[DEBUG] UnionID: ${this.unionid}`);
                    console.log(`[DEBUG] MemberID: ${this.memberId}`);
                }

                // 保存到缓存
                this.saveTokenCache();
                return true;
            } else {
                console.log(`登录失败: ${loginResponse.msg || '未知错误'}`);
                return false;
            }
        } catch (error) {
            console.log(`登录异常: ${error.message}`);
            return false;
        }
    }
