/*
小程序：清风纸悦
定时：一天1次
host=d2capplet.app.com.cn
export qingfeng="wxid"
用微信code就填wxid可混填
注：微信id不是wxid开头，请在前面加wxid=

下面的是选填
-------------------需要账号失效通知就设置-------------------
去 脚本管理=>sendNotify.js=>填想要通知的，
可加备注export tongzhi="备注"
-------------------代理配置--------------------
export ck变量名+dl='代理url'
export ck变量名+ys="1"   //关闭随机
export ck变量名+bf="1"   //打开并发
*/
process.noDeprecation = true;
NAME = `清风纸悦`; VALY = ['qingfeng']
CK = ``
let appToken = process.env.tongzhi || ''
let bingfa = process.env[VALY[0] + 'bf'] || '';
let yuanshikg = process.env[VALY[0] + 'ys'] || '1';
let dldz = process.env[VALY[0] + 'dl']
let host = 'd2capplet.app.com.cn'
let url = 'https://' + host
const Vacation = '1.0.0'
const envSplitor = ['\n', '@'] //支持多种分割
const LOGS = 0 //1开日志。0关闭
usid = 0
class Bar {
    constructor(str) {
        this.num = `|${++usid}|`
        this.one = 0
        this.strck = str
        this.token = str.split('#')[0]
    }
    async moshi() {
        if (!this.token.startsWith('wxid')) {
            console.log(`${this.num}：当前使用CK模式`);
            this.wxcode = 0
        } else {
            console.log(`${this.num}：当前使用code模式`)
            if (this.token.includes('wxid=')) {
                this.wxid = this.token.split('=')[1]
            } else {
                this.wxid = this.token
            }
            await this.readck()
            this.wxcode = 1;
            [this.token, this.phone] = this.ck.split('#')

        }
    }
    headers() {
        let header = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090b13) XWEB/9129',
            'authorization': this.token,
            'referer': 'https://servicewechat.com/wx6b9ceb6542d232be/288/page-frame.html',
        }
        return header;
    }
    async hqdl() {//获取代理
        let result = await $.task('get', dldz, { 'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36' })
        this.dlip = result.split('\n')[0]
        console.log(`账号${this.num}：代理IP：${this.dlip}`)
    }
    async user_task_list() {
        await $.wait($.RT(1000, 3000))
        await this.id()
    }
    async huoqucode() {
        let fn = "code"
        this.one = 1
        const refererKey = Object.keys(this.headers()).find(k => k.toLowerCase() === 'referer');
        const refererUrl = this.headers()[refererKey];
        this.appid = new URL(refererUrl).pathname.split('/')[1];
        let codeModule = require('./wxcode');
        let wxcode = await codeModule.getWxCode(this.wxid, this.appid);
        if (!wxcode.success) {
            console.log(`${this.num} ${fn}：${wxcode.error}`);
            return;
        }
        await this.logintoken(wxcode.code);
    }
    async logintoken(code) {
        let fn = "获取ck"
        await $.wait($.RT(1000, 3000));
        this.token = 'Bearer'
        let body = `{"code":"${code}"}`
        let data = await this.task('post', `${url}/miniprogram-api/sinar-api/auth/member/login`, this.headers(), body)
        if (data.code == 200) {
            this.token = 'Bearer ' + data.data.token

            this.ck = this.token + '#' + this.phone
            await this.writeck()
            await $.wait($.RT(3000, 5000))
        } else {
            let msg = data.msg
            console.log(`${this.num} ${fn}：${msg}`)
            await this.wxpusher(msg)
            return
        }

    }


    async login() {
        let fn = "用户信息"
        let data = await this.task('post', `${url}/miniprogram-api/sinar-api/member/info`, this.headers(), `{}`)
        if (data.code == 200) {
            this.isLogin = true
            let { nickName, point, base64NickName, avatarUrl, unionId } = data.data
            this.openid = data.data.openId
            this.unionId = unionId
            this.avatarUrl = avatarUrl
            this.nickname = base64NickName
            this.num = '|' + nickName + '|'
            console.log(`${this.num} 🪙：${point}`)
        } else {
            this.isLogin = false
            let msg = data.msg
            console.log(`${this.num} ${fn}：${msg}`)
            if (msg.includes('令牌已过期') && this.wxcode && !this.one) {
                await this.huoqucode();
                await $.wait($.RT(3000, 5000))
                await this.login();
            } else {
                await this.wxpusher(msg);
            }

        }
    }
    async id() {
        let fn = "ID"
        let data = await this.task('get', `${url}/miniprogram-api/admin/member/getRegisterByCode?activityCode=`, this.headers())
        if (data.code == 200) {
            this.activityId = data.data.giftGashaponPath
            await $.wait($.RT(3000, 5000))
            await this.location30()
        } else {
            let msg = data.msg
            console.log(`${this.num} ${fn}：${msg}`)
        }
    }
    async location30() {
        let fn = 'location30'
        let data = await task1('get', `${url}/activity/webapi/v2/redirect/jump2BrandCny?openId=${this.openid}&activityId=${this.activityId}&brand=jgapp&nickname=${this.nickname}&avatar=${this.avatarUrl}&tuid=${this.unionId}&third_ts=${$.time(13)}`, this.headers())

        if (data && data.request && data.request.uri && data.request.uri.href) {
            let api = data.request.uri.href.split('?')[1]
            await this.jgapp(api)
        } else {
            console.log(`${this.num} ${fn}：响应数据格式不正确`)
            console.log(`${this.num} ${fn}：响应数据：`, data)
        }

    
}
    async jgapp(api) {
    let fn = 'jgapp'
    let data = await this.task('get', `${url}/activity/webapi/v2/user/login?${api}`, this.headers())
    if (data.code == 0) {
        let token = data.data.token
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.179 Mobile Safari/537.36 XWEB/1380083 MMWEBSDK/20250503 MMWEBID/2783 MicroMessenger/8.0.61.2880(0x28003D57) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 miniProgram/wx6b9ceb6542d232be',
            'usertoken': token,
            'referer': 'https://d2capplet.app.com.cn/gashapon/?'+api
        }
        await $.wait($.RT(3000, 5000))
        await this.choujianglist()
    } else {
        let msg = data.msg
        console.log(`${this.num} ${fn}：${msg}`)
    }
}
    async choujianglist() {
    let fn = '抽奖列表'
    let data = await this.task('get', `${url}/activity/webapi/v2/activity/checkDrawPrize`, this.headers)
    if (data.code == 0) {
        let { usePlayCount } = data.data
        console.log(`${this.num} ${fn}：可抽奖次数=${usePlayCount}`)
        for (let i = 0; i < usePlayCount; i++) {
            await $.wait($.RT(1000, 3000))
            await this.choujiang()
            await $.wait($.RT(1000, 3000))
        }
    } else {
        let msg = data.msg
        console.log(`${this.num} ${fn}：${msg}`)
    }
}
    async choujiang() {
    let fn = '抽奖'
    let data = await this.task('post', `${url}/activity/webapi/v2/activity/drawPrize`, this.headers, `{}`)
    if (data.code == 0) {
        let { name } = data.data
        console.log(`${this.num} ${fn}：${name}`)

        // 定义排除关键词
        const exclusion_keywords = ["会员满", "企微满", "会员 满", "元优惠券", "优惠券", "悦币"]

        // 检查是否包含排除关键词
        const shouldNotify = !exclusion_keywords.some(keyword => name.includes(keyword))

        if (shouldNotify) {
            await this.wxpusher(name)
        }
    } else {
        let msg = data.msg
        console.log(`${this.num} ${fn}：${msg}`)
    }
}
    async xinxi() {
    // let fn = "用户信息"
    // let data = await this.task('get', `${url}/user_server/api/v1/integral/sum_integral`, this.headers())
    // if (data.code == 0) {
    //     let { sum } = data.data
    //     console.log(`${this.num} ⭐：${sum}`)
    // } else {
    //     let msg = data.msg
    //     console.log(`${this.num} ${fn}：${msg}`)
    // }
}
    async wxpusher(id) {//wxpusher微信通知
    var notify = require('./sendNotify');
    await notify.sendNotify(`${NAME}[${this.num}]:${id}${appToken}`, ``);
}


    async writeck() {
    let fn = "写入"
    const fs = require('fs')
    const filePath = `xg_${NAME}.json`
    let data = {}
    // 检查文件是否存在
    if (fs.existsSync(filePath)) {
        try {
            const fileContent = fs.readFileSync(filePath, 'utf8')
            data = JSON.parse(fileContent)
        } catch (err) {
            console.log(`${this.num}：读取文件失败`, err)
            return
        }
    }
    // 更新或添加数据
    data[this.wxid] = {
        "ck": this.ck
    }
    // 写入文件
    try {
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2))
        console.log(`${this.num}：成功写入CK`)
    } catch (err) {
        console.log(`${this.num}：写入文件失败`, err)
    }
}
    async readck() {
    let fn = "读取ck"
    const fs = require('fs')
    const filePath = `xg_${NAME}.json`
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
        console.log(`${this.num}：文件不存在，需要获取code`)
        await this.huoqucode()
        return
    }
    try {
        const fileContent = fs.readFileSync(filePath, 'utf8')
        const data = JSON.parse(fileContent)
        // 检查wxid是否存在
        if (data[this.wxid] && data[this.wxid].ck) {
            this.ck = data[this.wxid].ck
        } else {
            console.log(`${this.num}：未找到对应wxid的CK，需要获取code`)
            await this.huoqucode()
        }
    } catch (err) {
        console.log(`${this.num}：读取文件失败`, err)
        await this.huoqucode()
    }
}


    async task(method, taskurl, taskheader, taskbody) {
    method = method === 'delete' ? method.toUpperCase() : method;
    const request = require('request');
    let retryCount = 0;
    const maxRetries = 2;
    let data;
    let isProxy = !!dldz;

    while (retryCount <= maxRetries) {
        const httpget = {
            url: taskurl,
            headers: { ...taskheader },
            timeout: 20000,
            ...(isProxy && this.dlip ? { proxy: `http://${this.dlip}` } : {})
        };

        if (method === 'post') {
            ['Content-Type', 'content-type', 'Content-Length', 'content-length'].forEach(k => delete httpget.headers[k]);
            httpget.headers['content-type'] = $.safeGet(taskbody) ?
                'application/json;charset=utf-8' : 'application/x-www-form-urlencoded';
            if (taskbody) httpget.headers['content-length'] = $.lengthInUtf8Bytes(taskbody);
        } else if (method === 'get') {
            ['Content-Type', 'content-type', 'Content-Length', 'content-length'].forEach(k => delete httpget.headers[k]);
        }

        if (method.indexOf('T') >= 0) {
            httpget.form = JSON.parse(taskbody);
        } else {
            httpget.body = taskbody;
        }

        httpget.headers['Host'] = taskurl.replace('//', '/').split('/')[1];

        try {
            data = await new Promise((resolve, reject) => {
                request[method.toLowerCase()](httpget, (err, response, resData) => {
                    try {
                        if (LOGS == 1) {
                            console.log(`==================请求==================`)
                            console.log(JSON.stringify(httpget))
                            console.log(`==================返回==================`)
                            console.log(resData)
                        }
                    } catch (e) { }
                    if (!err) {
                        if ($.safeGet(resData)) {
                            resolve(JSON.parse(resData));
                        } else {
                            resolve(resData);
                        }
                    } else {
                        reject(err);
                    }
                });
            });
            // 请求成功，直接返回
            return data;
        } catch (e) {
            retryCount++;
            if (isProxy) {
                // 代理模式下，每次重试前都重新获取代理
                try {
                    await this.hqdl();
                } catch (e) { }
            }
            if (retryCount > maxRetries) {
                if (isProxy) {
                    console.log(`代理请求失败(已重试${maxRetries}次)`);
                } else {
                    console.log(`请检查网络设置(已重试${maxRetries}次)`);
                }
                return { code: "99" };
            } else {
                // 递增等待时间
                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
        }
    }
}
} $ = DD()
!(async () => {
    console.log(`🔔[${NAME}] 📅 ${$.timenow(3)}:${$.timenow(4)}:${$.timenow(5)}`);
    await $.ExamineCookie()
    const useProxy = !!dldz;
    console.log(`当前使用${useProxy ? '代理' : '本地'}网络运行脚本`);
    this.dlqk = useProxy ? 1 : 0;
    if (!bingfa) {
        for (let user of $.cookie_list) {
            console.log('-'.repeat(20))
            if (!LOGS && yuanshikg !== '1') {
                const delay = $.RT(1000, 1490000);
                console.log(`随机延迟${Math.round(delay * 0.001)}秒`);
                await $.wait(delay);
            }
            useProxy && await user.hqdl();
            await user.moshi()
            await user.login()
            if (user.isLogin) {
                await user.user_task_list()
                await user.xinxi()
            }
        }
    } else {
        if (!LOGS && yuanshikg !== '1') {
            const delay = $.RT(150000, 490000);
            console.log(`随机延迟${Math.round(delay * 0.001)}秒`);
            await $.wait(delay);
        }
        useProxy && await $.Multithreading('hqdl')
        console.log(`\n-------- 用户信息 --------`)
        await $.Multithreading('moshi')
        await $.Multithreading('login')
        let valid_user_list = $.cookie_list.filter(x => x.isLogin == true)
        if (valid_user_list.length == 0) {
            console.log(`Cookie格式错误 或 账号被禁封`)
            return
        } else {
            console.log(`\n-------- 任务列表 --------`)
            await $.Multithreading('user_task_list')
            console.log(`\n-------- 运行结果 --------`)
        }
        for (let user of $.cookie_list) {
            user.xinxi()
        }
    }
})()
    .catch(e => { console.log(e) })
    .finally(() => { })
function DD() {
    return new (class {
        constructor() {
            this.cookie_list = []
            this.message = ''
            this.CryptoJS = require('crypto-js')
            this.NodeRSA = require('node-rsa')
            this.request = require('request')
            this.Sha_Rsa = require('jsrsasign')
        }

        // 手机号脱敏显示
        maskPhone(phone) {
            if (phone && phone.length === 11) {
                return phone.substring(0, 3) + '****' + phone.substring(7);
            }
            return phone;
        }
        //多线程
        async Multithreading(taskName, id, thread) {
            let workGroup = []
            if (!thread) {
                thread = 1
            }
            while (thread--) {
                for (let user of $.cookie_list) {
                    workGroup.push(user[taskName](id))
                }
            }
            await Promise.allSettled(workGroup)
        }
        //变量检查
        ExamineCookie() {
            let ckStr = process.env[VALY] || CK
            let userCount = 0
            if (ckStr) {
                for (let sp of envSplitor) {
                    if (ckStr.includes(sp)) {
                        this.splitor = sp;
                        break;
                    }
                }
                for (let userCookies of ckStr.split(this.splitor).filter(x => !!x)) {
                    $.cookie_list.push(new Bar(userCookies))
                }
                userCount = $.cookie_list.length
            } else {
                console.log(`\n【${NAME}】：未填写变量: ${VALY}`)
            }
            console.log(`共找到${userCount}个账号`)
            return $.cookie_list
        }
        // 运行模块 get post put delete patch head options
        task(method, taskurl, taskheader, taskbody, taskhost) {
            if (method == 'delete') {
                method = method.toUpperCase()
            } else {
                method = method
            }
            if (method == 'post') {
                delete taskheader['content-type']
                delete taskheader['Content-type']
                delete taskheader['content-Type']
                if ($.safeGet(taskbody)) {
                    taskheader['Content-Type'] = 'application/json;charset=UTF-8'
                } else {
                    taskheader['Content-Type'] = 'application/x-www-form-urlencoded'
                }
                if (taskbody) {
                    taskheader['Content-Length'] = $.lengthInUtf8Bytes(taskbody)
                }
            }
            if (method == 'get') {
                delete taskheader['content-type']
                delete taskheader['Content-type']
                delete taskheader['content-Type']
                delete taskheader['Content-Length']
            }
            taskheader['Host'] = taskurl['replace']('//', '/')['split']('/')[1]
            return new Promise(async resolve => {
                if (method.indexOf('T') < 0) {
                    var httpget = {
                        url: taskurl,
                        headers: taskheader,
                        body: taskbody,
                        //  timeout: 6000,
                        proxy: 'http://' + taskhost,
                    }
                } else {
                    var httpget = {
                        url: taskurl,
                        headers: taskheader,
                        form: JSON.parse(taskbody),
                        //timeout: 6000,
                        proxy: 'http://' + taskhost,
                    }
                }
                if (!taskhost) {
                    delete httpget['proxy']
                }
                this.request[method.toLowerCase()](httpget, (err, response, data) => {
                    try {
                        if (data) {
                            if (LOGS == 1) {
                                console.log(`================ 请求 ================`)
                                console.log(httpget)
                                console.log(`================ 返回 ================`)
                                if ($.safeGet(data)) {
                                    console.log(JSON.parse(data))
                                } else {
                                    console.log(data)
                                }
                            }
                        }
                    } catch (e) {
                        console.log(e, taskurl + '\n' + taskheader)
                    } finally {
                        let datas = ''
                        if (!err) {
                            if ($.safeGet(data)) {
                                datas = JSON.parse(data)
                            } else if (data.indexOf('/') != -1 && data.indexOf('+') != -1) {
                                datas = $.decrypts(data)
                            } else {
                                datas = data
                            }
                        } else {
                            datas = taskurl + '   API请求失败，请检查网络重试\n' + err
                        }
                        return resolve(datas)
                    }
                })
            })
        }
        //body长度
        lengthInUtf8Bytes(str) {
            let m = encodeURIComponent(str).match(/%[89ABab]/g)
            return str.length + (m ? m.length : 0)
        }
        //随机数组
        randomArr(arr) {
            return arr[parseInt(Math.random() * arr.length, 10)];
        }
        //延迟
        wait(t) {
            return new Promise(e => setTimeout(e, t))
        }
        //当前时间戳s=10位时间戳或13位时间戳
        time(s) {
            if (s == 10) {
                return Math.round(+new Date() / 1000)
            } else {
                return +new Date()
            }
        }
        //时间戳格式化日期
        timenow(str) {
            let date = new Date()
            if (str == undefined) {
                let date = new Date(),
                    N = date.getFullYear() + '-',
                    Y = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-',
                    R = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() + ' ',
                    S = date.getHours() + ':',
                    F = date.getMinutes() + ':',
                    M = date.getSeconds() + 1 < 10 ? '0' + date.getSeconds() : date.getSeconds()
                return N + Y + R + S + F + M
            } else if (str == 0) {
                //年
                return date.getFullYear()
            } else if (str == 1) {
                //月
                return date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
            } else if (str == 2) {
                //日
                return date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
            } else if (str == 3) {
                //时
                return date.getHours()
            } else if (str == 4) {
                //分
                return date.getMinutes()
            } else if (str == 5) {
                //秒
                return date.getSeconds() + 1 < 10 ? '0' + date.getSeconds() : date.getSeconds()
            }
        }
        //数据检查
        safeGet(data) {
            try {
                if (typeof JSON.parse(data) == 'object') {
                    return true
                }
            } catch (e) {
                return false
            }
        }
        //随机字符
        SJS(len, t) {
            if (t == 0) {
                let chars = 'QWERTYUIOPASDFGHJKLZXCVBNM01234567890123456789'
                let maxLen = chars.length
                let str = ''
                for (let i = 0; i < len; i++) {
                    str += chars.charAt(Math.floor(Math.random() * maxLen))
                }
                return str
            } else if (t == 1) {
                let chars = 'qwertyuiopasdfghjklzxcvbnm0123456789'
                let maxLen = chars.length
                let str = ''
                for (let i = 0; i < len; i++) {
                    str += chars.charAt(Math.floor(Math.random() * maxLen))
                }
                return str
            } else if (t == 2) {
                let chars = 'QWERTYUIOPASDFGHJKLZXCVBNMqwertyuiopasdfghjklzxcvbnm0123456789'
                let maxLen = chars.length
                let str = ''
                for (let i = 0; i < len; i++) {
                    str += chars.charAt(Math.floor(Math.random() * maxLen))
                }
                return str
            } else {
                let chars = '0123456789'
                let maxLen = chars.length
                let str = ''
                for (let i = 0; i < len; i++) {
                    str += chars.charAt(Math.floor(Math.random() * maxLen))
                }
                return str
            }
        }
        //随机$.udid  0=大写  1=小写
        udid(str) {
            function S4() {
                return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
            }
            let uuid = S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4()
            if (str == 0) {
                return uuid.toUpperCase()
            } else {
                return uuid.toLowerCase()
            }
        }
        //  .toUpperCase()  转化大写
        //  .toLowerCase()  转化小写
        //console.log(...new Set(arr))去重
        //KEY = [...new Set(KEY.filter(item => !!item))]
        //filter(item => !!item) 过滤1!=1
        //Array.from()方法就是将一个类数组对象或者可遍历对象转换成一个真正的数组。
        //es编码  escape("中文")
        //es解码  unescape("%u4E2D%u6587")
        //URI编码  encodeURI("中文") 不完全
        //URI解码  decodeURI("%E4%B8%AD%E6%96%87")  不完全
        //URIC编码  encodeURIComponent("中文")
        //URIC解码  decodeURIComponent("%E4%B8%AD%E6%96%87")
        //str编码
        encodeUnicode(str) {
            var res = []
            for (var i = 0; i < str.length; i++) {
                res[i] = ('00' + str.charCodeAt(i).toString(16)).slice(-4)
            }
            return '\\u' + res.join('\\u')
        }
        //str解码
        decodeUnicode(str) {
            str = str.replace(/\\u/g, '%u')
            return unescape(unescape(str))
        }
        RT(X, Y) {
            return Math.round(Math.random() * (Y - X) + X)
        }
        //去除数组空值 console.log($.arrNull(['A', '', 'B', null, undefined, 'C', '  ']))
        arrNull(arr) {
            var r = arr.filter(s => {
                return s && s.trim()
            })
            return r
        }
        //国际标准时间
        nowtime() {
            return new Date(new Date().getTime() + new Date().getTimezoneOffset() * 60 * 1000 + 8 * 60 * 60 * 1000)
        }
        //日期格式化时间戳
        timecs() {
            let newtime = $.nowtime()
            if (JSON.stringify(newtime).indexOf(' ') >= 0) {
                newtime = newtime.replace(' ', 'T')
            }
            return new Date(newtime).getTime() - 8 * 60 * 60 * 1000
        }
        //键值对转json  $.rtjson(str, '&', '=',1)
        rtjson(input, apart, apart2, i) {
            if (i == 0) {
                return JSON.stringify(
                    input.split(apart).reduce((sum, item) => {
                        let temp = item.split(apart2)
                        sum[temp[0].trim()] = temp[1].trim()
                        return sum
                    }, {})
                )
            } else {
                return input.split(apart).reduce((sum, item) => {
                    let temp = item.split(apart2)
                    sum[temp[0].trim()] = temp[1].trim()
                    return sum
                }, {})
            }
        }
        //md5加密 0=32位小写,1=32位大写,2=中间16位小写,3=中间16位大写
        MD5Encrypt(i, str) {
            if (i == 0) {
                return this.CryptoJS.MD5(str).toString().toLowerCase()
            } else if (i == 1) {
                return this.CryptoJS.MD5(str).toString().toUpperCase()
            } else if (i == 2) {
                return this.CryptoJS.MD5(str).toString().substring(8, 24).toLowerCase()
            } else if (i == 3) {
                return this.CryptoJS.MD5(str).toString().substring(8, 24).toUpperCase()
            }
        }
        //SHA类：SHA1,SHA3,SHA224,SHA256,SHA384,SHA512,RIPEMD160
        //SHA类加密方式  使用：SHA_Encrypt(0, 'SHA1', str) 或 SHA_Encrypt(1,'SHA256', str)
        //0表示加密后编码，1直接使用hex格式
        SHA_Encrypt(i, Encrypt, str) {
            if (i == 0) {
                return this.CryptoJS[Encrypt](str).toString(this.CryptoJS.enc.Base64)
            } else {
                return this.CryptoJS[Encrypt](str).toString()
            }
        }
        //HmacSHA类：HmacSHA1,HmacSHA3,HmacSHA224,HmacSHA256,HmacSHA384,HmacSHA512,HmacRIPEMD160
        //0表示加密后编码，1直接使用hex格式
        //SHA类加密方式  使用：SHA_Encrypt(0,'HmacSHA1', str,key) 或 SHA_Encrypt(1,'HmacSHA256', str,key)
        HmacSHA_Encrypt(i, Encrypt, str, key) {
            if (i == 0) {
                return this.CryptoJS[Encrypt](str, key).toString(this.CryptoJS.enc.Base64)
            } else {
                return this.CryptoJS[Encrypt](str, key).toString()
            }
        }
        //Base64 0=编码 1=解码
        Base64(i, str) {
            if (i == 0) {
                return this.CryptoJS.enc.Base64.stringify(this.CryptoJS.enc.Utf8.parse(str))
            } else {
                return this.CryptoJS.enc.Utf8.stringify(this.CryptoJS.enc.Base64.parse(str))
            }
        }
        //AES/DES加解密 0=加密  1=解密
        //使用方法：DecryptCrypto(`0`,`AES`, `ECB`, `Pkcs7`, data, 'key', 'iv')
        DecryptCrypto(i, method, mode, padding, data, key, iv) {
            if (i == 0) {
                const encrypted = this.CryptoJS[method].encrypt(this.CryptoJS.enc.Utf8.parse(data), this.CryptoJS.enc.Utf8.parse(key), {
                    iv: this.CryptoJS.enc.Utf8.parse(iv),
                    mode: this.CryptoJS.mode[mode],
                    padding: this.CryptoJS.pad[padding],
                })
                return encrypted.toString()
            } else {
                const decrypt = this.CryptoJS[method].decrypt(data, this.CryptoJS.enc.Utf8.parse(key), {
                    iv: this.CryptoJS.enc.Utf8.parse(iv),
                    mode: this.CryptoJS.mode[mode],
                    padding: this.CryptoJS.pad[padding],
                })
                return decrypt.toString(this.CryptoJS.enc.Utf8)
            }
        }
        //RSA加密
        RSA(msg, Key) {
            const NodeRSA = require('node-rsa')
            let nodersa = new NodeRSA('-----BEGIN PUBLIC KEY-----\n' + Key + '\n-----END PUBLIC KEY-----')
            nodersa.setOptions({ encryptionScheme: 'pkcs1' })
            return nodersa.encrypt(msg, 'base64', 'utf8')
        }
        SHA_RSA(str, Keys) {
            let key = this.Sha_Rsa.KEYUTIL.getKey('-----BEGIN PRIVATE KEY-----\n' + $.getNewline(Keys, 76) + '\n-----END PRIVATE KEY-----')
            let signature = new this.Sha_Rsa.KJUR.crypto.Signature({ alg: 'SHA256withRSA' })
            signature.init(key)
            signature.updateString(str)
            let originSign = signature.sign()
            let sign64u = this.Sha_Rsa.hextob64u(originSign)
            return sign64u
        }
    })()
}
//RSA加密 RSA/ECB/PKCS1Padding
async function RSA(str, Key) {
    const NodeRSA = require('node-rsa')
    let nodersa = new NodeRSA('-----BEGIN PUBLIC KEY-----\n' + Key + '\n-----END PUBLIC KEY-----')
    nodersa.setOptions({ encryptionScheme: 'pkcs1' })
    return nodersa.encrypt(str, 'base64', 'utf8')
}
// 示例用法,1输出不补零,0输出格式为 HH:mm:ss
function setTime(i) {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();
    const formattedHours = (i === 0) ? String(hours).padStart(2, '0') : String(hours);
    const formattedMinutes = (i === 0) ? String(minutes).padStart(2, '0') : String(minutes);
    const formattedSeconds = (i === 0) ? String(seconds).padStart(2, '0') : String(seconds);
    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
}
//日期
function generate(i) {
    let month = ''
    const now = new Date();
    const year = now.getFullYear();
    if (i == 0) {
        month = String(now.getMonth() + 1).padStart(2, '0');
    } else {
        month = String(now.getMonth() + 1);
    }
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}
async function task1(method, taskurl, taskheader, taskbody) {//代理请求模块
    if (method == 'delete') {
        method = method.toUpperCase()
    } else {
        method = method
    }
    const request = require('request')
    if (method == 'post') {
        delete taskheader['Content-Type']
        delete taskheader['content-type']
        delete taskheader['Content-Length']
        delete taskheader['content-length']
        if ($.safeGet(taskbody)) {
            taskheader['content-type'] = 'application/json;charset=utf-8'
        } else {
            taskheader['content-type'] = 'application/x-www-form-urlencoded'
        }
        if (taskbody) {
            taskheader['content-length'] = $.lengthInUtf8Bytes(taskbody)
        }
    }
    if (method == 'get') {
        delete taskheader['Content-Type']
        delete taskheader['content-length']
        delete taskheader['content-type']
        delete taskheader['Content-Length']
    }
    taskheader['Host'] = taskurl['replace']('//', '/')['split']('/')[1]
    if (dldz == undefined) {
        if (method.indexOf('T') < 0) {
            var httpget = {
                url: taskurl,
                headers: taskheader,
                body: taskbody,
                // proxy:`http://${this.dlip}`,
                timeout: 20000,
                followRedirect: false
            }
        } else {
            var httpget = {
                url: taskurl,
                headers: taskheader,
                form: JSON.parse(taskbody),
                // proxy:`http://${this.dlip}`,
                timeout: 20000,
                followRedirect: false
            }
        }
    } else {
        if (method.indexOf('T') < 0) {
            var httpget = {
                url: taskurl,
                headers: taskheader,
                body: taskbody,
                proxy: `http://${this.dlip}`,
                timeout: 20000,
                followRedirect: false
            }
        } else {
            var httpget = {
                url: taskurl,
                headers: taskheader,
                form: JSON.parse(taskbody),
                proxy: `http://${this.dlip}`,
                timeout: 20000,
                followRedirect: false
            }
        }
    }
    return new Promise(async resolve => {
        request[method.toLowerCase()](httpget, async (err, response, data) => {
            try {
                if (LOGS == 1) {
                    console.log(`==================请求==================`)
                    console.log(JSON.stringify(httpget))
                    console.log(`==================返回==================`)
                    console.log(err)
                    console.log(JSON.stringify(response))
                    // console.log(data)
                }
            } catch (e) {
            } finally {
                if (!err) {
                    // 检查是否是302重定向
                    if (response && response.statusCode === 302) {
                        const location = response.headers.location;
                        // console.log(`检测到302重定向，Location: ${location}`);
                        data = {
                            request: {
                                uri: {
                                    href: location
                                }
                            }
                        };
                    } else if ($.safeGet(data)) {
                        data = JSON.parse(data)
                    } else {
                        data = data
                    }
                } else {
                    if (dldz == undefined) {
                        console.log(`请检查网络设置`)
                        data = { code: "99" }
                    } else {
                        console.log(`代理请求失败`)
                        data = { code: "99" }
                    }
                }
                return resolve(data)
            }
        })
    })
}